const logger = require('../utils/AsyncLogger')

/**
 * 游戏进度管理器
 * 负责管理游戏进度的保存、加载和同步功能
 */
class GameProgressManager {
  constructor(main) {
    this.main = main
    this.DEBUG_MODE = false
  }

  /**
   * 保存关卡完成记录
   */
  saveLevelCompletion() {
    try {
      const playTime = Math.floor((this.main.endTime - this.main.startTime) / 1000) // 转换为秒
      const record = {
        score: this.main.score,
        moves: this.main.moves,
        playTime: playTime,
        timestamp: Date.now()
      }
      
      if (this.DEBUG_MODE) logger.info(`📱 保存关卡 ${this.main.currentLevel} 完成记录:`, record)
      
      // 使用存档管理器保存关卡完成记录（本地）
      this.main.saveManager.levelCompleted(this.main.currentLevel, record)
      
      // 检查是否为开发者模式
      if (this.main.saveManager.isDeveloperMode()) {
        logger.info('🛠️ 开发者模式：跳过服务器记录同步')
        return
      }
      
      // 如果用户已登录服务器，同时保存到服务器
      this.syncToServer(record)
      
    } catch (error) {
      logger.error('❌ 保存关卡记录失败:', error)
    }
  }

  /**
   * 同步记录到服务器
   * @param {Object} record - 关卡记录
   */
  async syncToServer(record) {
    if (this.main.userManager && this.main.userManager.isConnectedToServer()) {
      logger.info(`🌐 保存关卡${this.main.currentLevel}记录到服务器...`)
      
      try {
        const result = await this.main.userManager.saveLevelProgress(
          this.main.currentLevel,
          this.main.score,
          this.main.moves,
          record.playTime
        )
        
        if (result.success) {
          logger.info(`✅ 关卡${this.main.currentLevel}记录已同步到服务器`)
          // 如果是最佳记录，显示提示
          if (result.data && result.data.isBestRecord) {
            this.main.messageManager.showMessage(`🎉 刷新了关卡${this.main.currentLevel}的最佳记录！`, 3000)
          }
        } else {
          logger.warn(`⚠️ 关卡${this.main.currentLevel}记录同步到服务器失败:`, result.error)
        }
      } catch (error) {
        logger.error(`❌ 关卡${this.main.currentLevel}记录同步到服务器异常:`, error)
      }
    } else {
      logger.info('🔒 未连接到服务器，仅保存本地记录')
    }
  }

  /**
   * 加载游戏进度
   */
  loadGameProgress() {
    try {
      // 从本地存档加载进度
      const savedData = this.main.saveManager.loadGame()
      
      if (savedData) {
        // 恢复游戏状态
        this.main.currentLevel = savedData.currentLevel || 1
        this.main.isFirstPlay = savedData.isFirstPlay !== false // 默认为true
        
        logger.info(`📱 加载本地游戏进度: 关卡${this.main.currentLevel}, 首次游戏: ${this.main.isFirstPlay}`)
      } else {
        // 首次游戏
        this.main.currentLevel = 1
        this.main.isFirstPlay = true
        logger.info('🆕 首次游戏，初始化默认进度')
      }
      
      // 如果不是开发者模式，尝试从服务器加载进度
      if (!this.main.saveManager.isDeveloperMode()) {
        this.loadServerProgress()
      } else {
        logger.info('🛠️ 开发者模式：跳过服务器进度加载')
      }
      
    } catch (error) {
      logger.error('❌ 加载游戏进度失败:', error)
      // 使用默认值
      this.main.currentLevel = 1
      this.main.isFirstPlay = true
    }
  }

  /**
   * 从服务器加载进度
   */
  async loadServerProgress() {
    if (!this.main.userManager || !this.main.userManager.isConnectedToServer()) {
      logger.info('🔒 未连接到服务器，使用本地进度')
      return
    }

    try {
      logger.info('🌐 从服务器加载游戏进度...')
      const serverProgress = await this.main.userManager.getGameProgress()
      
      if (serverProgress && serverProgress.success && serverProgress.data) {
        const data = serverProgress.data
        
        // 比较本地和服务器进度，使用较高的关卡
        if (data.maxLevel > this.main.currentLevel) {
          this.main.currentLevel = data.maxLevel
          logger.info(`✅ 使用服务器进度: 关卡${this.main.currentLevel}`)
          
          // 更新本地存档
          this.main.saveManager.saveGame({
            currentLevel: this.main.currentLevel,
            isFirstPlay: false
          })
        } else {
          logger.info(`📱 本地进度更高，保持关卡${this.main.currentLevel}`)
        }
        
        this.main.isFirstPlay = false
      } else {
        logger.info('🔒 服务器无进度数据，使用本地进度')
      }
    } catch (error) {
      logger.error('❌ 从服务器加载进度失败:', error)
    }
  }

  /**
   * 保存当前游戏状态
   */
  saveCurrentState() {
    try {
      const gameData = {
        currentLevel: this.main.currentLevel,
        isFirstPlay: this.main.isFirstPlay,
        lastPlayTime: Date.now()
      }
      
      this.main.saveManager.saveGame(gameData)
      logger.info('💾 当前游戏状态已保存')
    } catch (error) {
      logger.error('❌ 保存游戏状态失败:', error)
    }
  }

  /**
   * 重置游戏进度
   */
  resetProgress() {
    try {
      this.main.currentLevel = 1
      this.main.isFirstPlay = true
      this.main.score = 0
      this.main.moves = 0
      this.main.moveHistory = []
      
      // 重置道具次数
      this.main.powerUps = {
        undo: 3,
        shuffle: 2
      }
      
      // 清除本地存档
      this.main.saveManager.clearSave()
      
      logger.info('🔄 游戏进度已重置')
      this.main.messageManager.showMessage('游戏进度已重置', 2000)
    } catch (error) {
      logger.error('❌ 重置游戏进度失败:', error)
    }
  }

  /**
   * 设置调试模式
   * @param {boolean} enabled - 是否启用调试模式
   */
  setDebugMode(enabled) {
    this.DEBUG_MODE = enabled
  }
}

module.exports = GameProgressManager
